name: Publish to PyPI

on:
  release:
    types: [published]

permissions:
  contents: read
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: publish-to-pypi

    steps:
      - uses: actions/checkout@v2

      # Make sure tags are fetched, so we can get a version.
      - run: |
          git fetch --prune --unshallow --tags

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install --upgrade build twine wheel

      - name: Build
        run: |
          python -m build
          python -m wheel tags --platform-tag manylinux1_x86_64 dist/*any.whl
          rm dist/*any.whl

      - name: Publish
        uses: pypa/gh-action-pypi-publish@release/v1