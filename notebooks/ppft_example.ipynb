{"cells": [{"cell_type": "markdown", "id": "d96a39e2", "metadata": {}, "source": ["# PPFT example notebook\n", "This notebook contains a toy example of training with the PPFT (property prediction fine-tuning) loss. In this example, the pretrained BioEmu model is finetuned to modify the proportion of folded states that it samples for a single protein.\n", "\n", "Finetuning in this narrow way may well have detrimental effects on model performance. In [our own work](https://doi.org/10.1101/2024.12.05.626885) when we finetuned with PPFT, we interspersed PPFT weight updates with standard denoising score-matching updates using structures of a large variety of proteins."]}, {"cell_type": "code", "execution_count": null, "id": "2642b880", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "OUTPUT_DIR = Path(\"~/ppft_example_output\").expanduser()\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "repo_dir = Path(\"~/bioemu\").expanduser()\n", "rollout_config_path = repo_dir / \"notebooks\" / \"rollout.yaml\"\n", "# Reference structure downloaded from https://zenodo.org/records/7992926\n", "reference_pdb = repo_dir / \"notebooks\" / \"HHH_rd1_0335.pdb\"\n", "assert reference_pdb.exists(), f\"Reference structure not found at {reference_pdb}\""]}, {"cell_type": "code", "execution_count": null, "id": "cee3852e", "metadata": {}, "outputs": [], "source": ["# Compute reference contacts using the reference structure.\n", "from bioemu.training.foldedness import TargetInfo, compute_contacts\n", "import mdtraj\n", "\n", "\n", "reference_traj = mdtraj.load(reference_pdb)\n", "reference_traj = reference_traj.atom_slice(reference_traj.topology.select(\"name CA\"))\n", "reference_info = compute_contacts(traj=reference_traj)"]}, {"cell_type": "code", "execution_count": null, "id": "19d4881e", "metadata": {}, "outputs": [], "source": ["from bioemu.sample import main as sample_main\n", "from bioemu.training.foldedness import compute_fnc_for_list, foldedness_from_fnc\n", "import torch\n", "from bioemu.chemgraph import ChemGraph\n", "from matplotlib import pyplot as plt\n", "\n", "STEEPNESS = 10.0\n", "P_FOLD_THR = 0.5\n", "\n", "\n", "def sample_and_compute_metrics(\n", "    output_dir: Path,\n", "    ckpt_path: Path | None = None,\n", "    model_config_path: Path | None = None,\n", "    denoiser_config_path: Path | None = None,\n", "    num_samples: int = 300,\n", ") -> tuple[torch.Tensor, torch.Tensor]:\n", "    \"\"\"Generate samples and compute their FNC and foldedness metrics.\n", "\n", "    Returns:\n", "        tuple: (fnc, foldedness) tensors\n", "    \"\"\"\n", "    # Generate samples.\n", "    sample_main(\n", "        sequence=reference_info.sequence,\n", "        num_samples=num_samples,\n", "        output_dir=output_dir,\n", "        filter_samples=False,\n", "        ckpt_path=ckpt_path,\n", "        model_config_path=model_config_path,\n", "        denoiser_config_path=denoiser_config_path,\n", "    )\n", "\n", "    # Compute fraction of native contacts (FNC) and foldedness of the samples.\n", "    traj = mdtraj.load(output_dir / \"samples.xtc\", top=output_dir / \"topology.pdb\")\n", "    traj = traj.atom_slice(traj.topology.select(\"name CA\"))\n", "    chemgraph_list = [\n", "        ChemGraph(pos=torch.tensor(traj.xyz[i]), sequence=reference_info.sequence)\n", "        for i in range(len(traj))\n", "    ]\n", "    fnc = compute_fnc_for_list(batch=chemgraph_list, reference_info=reference_info)\n", "    foldedness = foldedness_from_fnc(fnc=fnc, p_fold_thr=P_FOLD_THR, steepness=STEEPNESS)\n", "\n", "    return fnc, foldedness\n", "\n", "\n", "def sample_and_plot(\n", "    output_dir: Path,\n", "    ckpt_path: Path | None = None,\n", "    model_config_path: Path | None = None,\n", "    denoiser_config_path: Path | None = None,\n", "    num_samples: int = 300,\n", ") -> None:\n", "    \"\"\"Generate samples, compute metrics, and plot their distributions.\"\"\"\n", "\n", "    # Get metrics\n", "    fnc, foldedness = sample_and_compute_metrics(\n", "        output_dir=output_dir,\n", "        ckpt_path=ckpt_path,\n", "        model_config_path=model_config_path,\n", "        denoiser_config_path=denoiser_config_path,\n", "        num_samples=num_samples,\n", "    )\n", "\n", "    # Plot the results.\n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(fnc.numpy(), bins=50, density=True)\n", "    plt.xlim(0, 1)\n", "    plt.xlabel(\"FNC\")\n", "    plt.ylabel(\"Density\")\n", "    plt.title(\"Fraction of native contacts\")\n", "    plt.text(\n", "        0.5,\n", "        0.9,\n", "        f\"Mean FNC: {fnc.mean().item():.3f}\",\n", "        ha=\"center\",\n", "        va=\"center\",\n", "        transform=plt.gca().transAxes,\n", "    )\n", "\n", "    plt.subplot(1, 2, 2)\n", "    plt.hist(foldedness.numpy(), bins=50, density=True)\n", "    plt.xlim(0, 1)\n", "    plt.xlabel(\"Foldedness\")\n", "    plt.ylabel(\"Density\")\n", "    plt.title(\"Foldedness\")\n", "    plt.text(\n", "        0.5,\n", "        0.9,\n", "        f\"Mean foldedness: {foldedness.mean().item():.2f}\",\n", "        ha=\"center\",\n", "        va=\"center\",\n", "        transform=plt.gca().transAxes,\n", "    )\n", "\n", "    plt.savefig(output_dir / \"histograms.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "cef7b926", "metadata": {}, "outputs": [], "source": ["# Sample and plot distributions of FNC and foldedness using default checkpoint and denoiser config.\n", "pretrained_samples_dir = OUTPUT_DIR / \"pretrained_samples\"\n", "sample_and_plot(output_dir=pretrained_samples_dir, ckpt_path=None)\n", "\n", "# For comparison, also show FNC and foldedness when sampling with the fast 'rollout' denoiser.\n", "plt.figure()\n", "sample_and_plot(\n", "    output_dir=OUTPUT_DIR / \"pretrained_samples_fast\",\n", "    ckpt_path=None,\n", "    denoiser_config_path=rollout_config_path,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "1ab59f71", "metadata": {}, "outputs": [], "source": ["import shutil\n", "import yaml\n", "\n", "import torch.nn as nn\n", "\n", "from bioemu.model_utils import load_model, load_sdes, maybe_download_checkpoint\n", "from bioemu.training.loss import calc_ppft_loss\n", "from bioemu.sample import get_context_chemgraph\n", "\n", "rollout_config = yaml.safe_load(rollout_config_path.read_text())\n", "\n", "\n", "def freeze_parameters_check_missing(model: nn.<PERSON>, exclude_patterns: list[str]) -> None:\n", "    \"\"\"\n", "    Freeze parameters of a model based on the provided patterns.\n", "\n", "    Args:\n", "        model: The model whose parameters are to be frozen.\n", "        exclude_patterns: The list of patterns to exclude from freezing.\n", "\n", "    Raises:\n", "        ValueError: If any pattern in exclude_patterns is not found in the model's parameters.\n", "    \"\"\"\n", "\n", "    # Patterns of parameters to exclude.\n", "    found_patterns = set()\n", "    for prm_label, prm in model.named_parameters():\n", "        # Set requires_grad based on the presence of the pattern\n", "        requires_grad = any([p in prm_label for p in exclude_patterns])\n", "        prm.requires_grad = requires_grad\n", "        # Update found_patterns set if the pattern is found\n", "        if requires_grad:\n", "            found_patterns.update({p for p in exclude_patterns if p in prm_label})\n", "\n", "    # Calculate the missing patterns by subtracting found_patterns from exclude_patterns\n", "    missing_patterns = set(exclude_patterns) - found_patterns\n", "    # Raise an error if any pattern in exclude_patterns is not found in the model's parameters\n", "    if missing_patterns:\n", "        raise ValueError(\n", "            f\"The following patterns to fine-tune were not found in the model: {', '.join(missing_patterns)}\"\n", "        )\n", "\n", "\n", "def finetune_with_target(\n", "    p_fold_target: float, n_steps_train: int, seed: int = 42\n", ") -> tuple[Path, Path]:\n", "    \"\"\"Finetune the pretrained BioEmu model to sample structures with mean foldedness p_fold_target.\"\"\"\n", "    output_dir = OUTPUT_DIR / f\"target_{p_fold_target:.2f}_seed_{seed}\"\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    torch.manual_seed(seed)\n", "    target_info = TargetInfo(\n", "        p_fold_thr=P_FOLD_THR, steepness=STEEPNESS, p_fold_target=p_fold_target\n", "    )\n", "\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "    ckpt_path, model_config_path = maybe_download_checkpoint(model_name=\"bioemu-v1.0\")\n", "    shutil.copy(model_config_path, output_dir / \"config.yaml\")\n", "    model_config_path = output_dir / \"config.yaml\"\n", "    sdes = load_sdes(model_config_path=model_config_path)\n", "    score_model = load_model(ckpt_path=ckpt_path, model_config_path=model_config_path).to(device)\n", "    freeze_parameters_check_missing(\n", "        score_model, exclude_patterns=[\"encoder.layers.0\", \"encoder.layers.7\"]\n", "    )\n", "    score_model.train()\n", "    system_id = reference_pdb.stem\n", "    chemgraph = (\n", "        get_context_chemgraph(sequence=reference_info.sequence)\n", "        .replace(system_id=system_id)\n", "        .to(device)\n", "    )\n", "    assert chemgraph.sequence == reference_info.sequence, (\n", "        \"ChemGraph sequence does not match reference sequence\"\n", "    )\n", "    optimizer = torch.optim.Adam(\n", "        score_model.parameters(),\n", "        lr=1e-5,\n", "        eps=1e-6,\n", "    )\n", "\n", "    # Track training metrics\n", "    train_losses = []\n", "    val_iterations = []\n", "    val_mean_foldedness = []\n", "\n", "    # Validation function\n", "    def validate(iteration: int, num_samples: int = 100) -> float:\n", "        \"\"\"Generate samples and compute mean foldedness for validation.\"\"\"\n", "        print(f\"Running validation at iteration {iteration}...\")\n", "        val_dir = output_dir / f\"val_iter_{iteration}\"\n", "        val_dir.mkdir(exist_ok=True)\n", "\n", "        # Save current model state temporarily\n", "        temp_ckpt = val_dir / \"temp_model.ckpt\"\n", "        torch.save(score_model.state_dict(), temp_ckpt)\n", "\n", "        # Generate validation samples and compute metrics\n", "        fnc, foldedness = sample_and_compute_metrics(\n", "            output_dir=val_dir,\n", "            ckpt_path=temp_ckpt,\n", "            model_config_path=model_config_path,\n", "            denoiser_config_path=rollout_config_path,\n", "            num_samples=num_samples,\n", "        )\n", "\n", "        mean_fold = foldedness.mean().item()\n", "        print(f\"Validation at iteration {iteration}: mean foldedness = {mean_fold:.3f}\")\n", "\n", "        # Clean up temporary checkpoint\n", "        temp_ckpt.unlink()\n", "\n", "        return mean_fold\n", "\n", "    for i in range(n_steps_train):\n", "        print(f\"Iteration {i + 1}/{n_steps_train}\")\n", "        optimizer.zero_grad()\n", "        loss = calc_ppft_loss(\n", "            score_model=score_model,\n", "            sdes=sdes,\n", "            batch=[chemgraph] * 10,\n", "            n_replications=4,\n", "            mid_t=rollout_config[\"mid_t\"],\n", "            target_info_lookup={system_id: target_info},\n", "            N_rollout=rollout_config[\"N_rollout\"],\n", "            record_grad_steps=rollout_config[\"record_grad_steps\"],\n", "            reference_info_lookup={system_id: reference_info},\n", "        )\n", "        loss.backward()\n", "        assert not torch.isnan(loss).any(), \"Loss contains NaN values\"\n", "        optimizer.step()\n", "\n", "        # Record training loss\n", "        train_losses.append(loss.item())\n", "        print(f\"  Loss: {loss.item():.4f}\")\n", "\n", "        # Run validation periodically.\n", "        if i % 20 == 0:\n", "            with torch.random.fork_rng():\n", "                # fork_rng because generate_batch called during 'validate' sets the random seed to a fixed value.\n", "                mean_fold = validate(i, num_samples=100)\n", "                val_iterations.append(i)\n", "                val_mean_foldedness.append(mean_fold)\n", "\n", "    checkpoint_path = output_dir / f\"step_{i + 1}.ckpt\"\n", "    torch.save(score_model.state_dict(), checkpoint_path)\n", "    print(f\"Model saved to {checkpoint_path}, config at {model_config_path}\")\n", "\n", "    # Plot training curves\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "    # Plot loss curve\n", "    ax1.plot(range(1, len(train_losses) + 1), train_losses)\n", "    ax1.set_xlabel(\"Iteration\")\n", "    ax1.set_ylabel(\"Loss\")\n", "    ax1.set_title(\"Training Loss\")\n", "    ax1.grid(True)\n", "\n", "    # Plot validation curve\n", "    ax2.plot(val_iterations, val_mean_foldedness, \"o-\")\n", "    ax2.axhline(y=p_fold_target, color=\"r\", linestyle=\"--\", label=f\"Target: {p_fold_target}\")\n", "    ax2.set_xlabel(\"Iteration\")\n", "    ax2.set_ylabel(\"Mean Foldedness\")\n", "    ax2.set_title(\"Validation: Mean Foldedness\")\n", "    ax2.grid(True)\n", "    ax2.legend()\n", "\n", "    plt.tight_layout()\n", "    plt.savefig(output_dir / \"training_curves.png\")\n", "    plt.show()\n", "\n", "    return checkpoint_path, model_config_path\n", "\n", "\n", "finetuned_checkpoint_path, model_config_path = finetune_with_target(\n", "    p_fold_target=0.5, n_steps_train=200\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8c270d14", "metadata": {}, "outputs": [], "source": ["plt.figure()\n", "sample_and_plot(\n", "    output_dir=finetuned_checkpoint_path.with_suffix(\".samples\"),\n", "    ckpt_path=finetuned_checkpoint_path,\n", "    model_config_path=model_config_path,\n", ")\n", "plt.figure()\n", "sample_and_plot(\n", "    output_dir=finetuned_checkpoint_path.with_suffix(\".rollout_samples\"),\n", "    ckpt_path=finetuned_checkpoint_path,\n", "    model_config_path=model_config_path,\n", "    denoiser_config_path=rollout_config_path,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "bioemu", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}